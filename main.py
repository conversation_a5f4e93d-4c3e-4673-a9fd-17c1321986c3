"""
SKUAST RAG Chatbot - FastAPI Application
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

from src.pdf_processor import PDFProcessor
from src.text_chunker import TextChunker
from src.vector_db import VectorDatabase
from src.retriever import SemanticRetriever
from src.gemini_client import GeminiClient

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for components
vector_db: Optional[VectorDatabase] = None
retriever: Optional[SemanticRetriever] = None
gemini_client: Optional[GeminiClient] = None
system_initialized = False


# Pydantic models
class ChatRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="User query about SKUAST")
    top_k: int = Field(default=3, ge=1, le=10, description="Number of relevant chunks to retrieve")
    temperature: float = Field(default=0.3, ge=0.0, le=1.0, description="Temperature for response generation")


class ChatResponse(BaseModel):
    response: str = Field(..., description="AI-generated response")
    success: bool = Field(..., description="Whether the request was successful")
    sources: list = Field(default=[], description="Source chunks used for the response")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")
    error: Optional[str] = Field(default=None, description="Error message if any")


class SystemStatus(BaseModel):
    status: str = Field(..., description="Overall system status")
    components: Dict[str, Any] = Field(..., description="Status of individual components")
    statistics: Dict[str, Any] = Field(default={}, description="System statistics")


async def initialize_system():
    """Initialize all system components."""
    global vector_db, retriever, gemini_client, system_initialized
    
    try:
        logger.info("Initializing SKUAST RAG system...")
        
        # Initialize vector database
        logger.info("Initializing vector database...")
        vector_db = VectorDatabase()
        
        # Try to load existing index
        if vector_db.load_index("skuast_index"):
            logger.info("Loaded existing FAISS index")
        else:
            logger.info("No existing index found. Will need to process PDF first.")
        
        # Initialize retriever
        logger.info("Initializing semantic retriever...")
        retriever = SemanticRetriever(vector_db)
        
        # Initialize Gemini client
        logger.info("Initializing Gemini client...")
        api_key = "AIzaSyDTDwmuo0NieUB4Xx_SwA25OvRugA1JyBQ"
        if not api_key:
            logger.warning("GOOGLE_API_KEY not found in environment variables")
            raise ValueError("Google API key not configured")
        
        gemini_client = GeminiClient(api_key=api_key)
        
        # Validate Gemini connection
        if not gemini_client.validate_api_key():
            raise ValueError("Failed to validate Google API key")
        
        system_initialized = True
        logger.info("System initialization completed successfully")
        
    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        system_initialized = False
        raise


async def process_pdf_if_needed():
    """Process PDF and build index if not already done."""
    global vector_db
    
    if not vector_db or vector_db.index is not None:
        return  # Index already exists
    
    pdf_path = "skuast.pdf"
    if not Path(pdf_path).exists():
        logger.error(f"PDF file not found: {pdf_path}")
        return
    
    try:
        logger.info("Processing PDF and building vector index...")
        
        # Process PDF
        pdf_processor = PDFProcessor()
        markdown_content = pdf_processor.process_pdf(pdf_path)
        
        # Chunk text
        chunker = TextChunker(chunk_size=5000, overlap_size=1000)
        chunks = chunker.split_text_into_chunks(markdown_content)
        
        # Build vector index
        vector_db.build_index(chunks)
        vector_db.save_index("skuast_index")
        
        logger.info(f"Successfully processed PDF and created index with {len(chunks)} chunks")
        
    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    # Startup
    await initialize_system()
    await process_pdf_if_needed()
    yield
    # Shutdown
    logger.info("Shutting down SKUAST RAG system")


# Create FastAPI app
app = FastAPI(
    title="SKUAST RAG Chatbot",
    description="A Retrieval-Augmented Generation chatbot for SKUAST (Sher-e-Kashmir University of Agricultural Sciences and Technology)",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with basic information."""
    return {
        "message": "SKUAST RAG Chatbot API",
        "description": "Ask questions about Sher-e-Kashmir University of Agricultural Sciences and Technology",
        "endpoints": {
            "chat": "POST /chat - Ask questions about SKUAST",
            "status": "GET /status - Check system status",
            "health": "GET /health - Health check"
        }
    }


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Chat endpoint for asking questions about SKUAST.
    """
    if not system_initialized:
        raise HTTPException(
            status_code=503,
            detail="System not initialized. Please check system status."
        )
    
    if not all([vector_db, retriever, gemini_client]):
        raise HTTPException(
            status_code=503,
            detail="System components not available"
        )
    
    try:
        logger.info(f"Processing chat request: '{request.query[:50]}...'")
        
        # Create context using retriever
        context_data = retriever.create_context_for_llm(
            query=request.query,
            top_k=request.top_k
        )
        
        if context_data['num_sources'] == 0:
            return ChatResponse(
                response="I don't have any relevant information from the SKUAST document to answer your question. Please try rephrasing your question or ask about topics covered in the SKUAST documentation.",
                success=True,
                sources=[],
                metadata={
                    "query": request.query,
                    "num_sources": 0,
                    "message": "No relevant context found"
                }
            )
        
        # Generate response using Gemini
        gemini_response = gemini_client.generate_response(
            query=request.query,
            context=context_data['context'],
            temperature=request.temperature
        )
        
        if not gemini_response['success']:
            return ChatResponse(
                response=gemini_response['response'],
                success=False,
                sources=context_data['sources'],
                error=gemini_response.get('error'),
                metadata={
                    "query": request.query,
                    "num_sources": context_data['num_sources']
                }
            )
        
        # Successful response
        return ChatResponse(
            response=gemini_response['response'],
            success=True,
            sources=context_data['sources'],
            metadata={
                "query": request.query,
                "num_sources": context_data['num_sources'],
                "avg_relevance_score": context_data.get('avg_relevance_score', 0),
                "gemini_metadata": gemini_response.get('metadata', {})
            }
        )
        
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@app.get("/status", response_model=SystemStatus)
async def get_status():
    """Get system status and component information."""
    try:
        components = {}
        
        # Vector database status
        if vector_db:
            components["vector_database"] = vector_db.get_index_stats()
        else:
            components["vector_database"] = {"status": "Not initialized"}
        
        # Retriever status
        if retriever:
            components["retriever"] = retriever.get_retrieval_stats()
        else:
            components["retriever"] = {"status": "Not initialized"}
        
        # Gemini client status
        if gemini_client:
            components["gemini_client"] = gemini_client.get_model_info()
        else:
            components["gemini_client"] = {"status": "Not initialized"}
        
        # Overall status
        overall_status = "Ready" if system_initialized else "Not Ready"
        
        # Statistics
        statistics = {
            "system_initialized": system_initialized,
            "pdf_processed": vector_db.index is not None if vector_db else False,
            "api_key_configured": os.getenv('GOOGLE_API_KEY') is not None
        }
        
        return SystemStatus(
            status=overall_status,
            components=components,
            statistics=statistics
        )
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving system status: {str(e)}"
        )


@app.get("/health")
async def health_check():
    """Simple health check endpoint."""
    return {
        "status": "healthy" if system_initialized else "unhealthy",
        "timestamp": "2024-01-01T00:00:00Z"  # You might want to use actual timestamp
    }


@app.post("/rebuild-index")
async def rebuild_index(background_tasks: BackgroundTasks):
    """Rebuild the vector index (admin endpoint)."""
    if not system_initialized:
        raise HTTPException(
            status_code=503,
            detail="System not initialized"
        )
    
    background_tasks.add_task(process_pdf_if_needed)
    
    return {
        "message": "Index rebuild started in background",
        "status": "processing"
    }


if __name__ == "__main__":
    import uvicorn
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
